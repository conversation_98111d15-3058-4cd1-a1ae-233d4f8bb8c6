#!/usr/bin/env python3
"""
RAG API Test Script

This script tests the RAG API endpoints to ensure they are working correctly.
"""

import sys
import os
sys.path.append('.')

def test_rag_api_imports():
    """Test that all RAG API components can be imported"""
    print("🧪 Testing RAG API imports...")
    
    try:
        from core.api.rag import router
        print("✅ RAG router imported successfully")
        print(f"   - Prefix: {router.prefix}")
        print(f"   - Tags: {router.tags}")
        
        from core.rag.ragflow import RagFlowRAGService
        print("✅ RagFlowRAGService imported successfully")
        
        from core.services.database.crud.user_uploaded_files import user_uploaded_files_crud
        print("✅ User uploaded files CRUD imported successfully")
        
        return True
        
    except Exception as e:
        print(f"❌ Import error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_rag_api_models():
    """Test that all RAG API models are properly defined"""
    print("\n🧪 Testing RAG API models...")
    
    try:
        from core.api.rag import (
            FileUploadResponse,
            FileStatusResponse,
            FileListResponse,
            DocumentRetrieveRequest,
            DocumentChunk,
            DocumentRetrieveResponse,
            RAGGenerateRequest,
            RAGGenerateResponse,
            FileBatchDeleteRequest,
            FileBatchDeleteResponse
        )
        
        print("✅ All RAG API models imported successfully")
        
        # Test model instantiation
        doc_request = DocumentRetrieveRequest(query="test query")
        print(f"✅ DocumentRetrieveRequest created: query='{doc_request.query}'")
        
        rag_request = RAGGenerateRequest(query="test rag query")
        print(f"✅ RAGGenerateRequest created: query='{rag_request.query}'")
        
        return True
        
    except Exception as e:
        print(f"❌ Model error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_application_setup():
    """Test that the main application includes RAG routes"""
    print("\n🧪 Testing application setup...")
    
    try:
        from main import app
        print("✅ Main application created successfully")
        
        # Check for RAG routes
        rag_routes = []
        for route in app.routes:
            if hasattr(route, 'path') and '/api/rag' in route.path:
                methods = getattr(route, 'methods', ['UNKNOWN'])
                rag_routes.append(f"{list(methods)} {route.path}")
        
        if rag_routes:
            print("✅ RAG routes found in application:")
            for route in rag_routes:
                print(f"   - {route}")
        else:
            print("⚠️  No RAG routes found - checking router registration...")
            
            # Check if router is in main router
            from core.api.router import router as main_router
            print(f"✅ Main router has {len(main_router.routes)} routes")
        
        return True
        
    except Exception as e:
        print(f"❌ Application setup error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_rag_service_config():
    """Test RAG service configuration"""
    print("\n🧪 Testing RAG service configuration...")
    
    try:
        from core.config.app_config import config
        
        # Check if RAG service config exists
        if hasattr(config, 'rag_services') and config.rag_services:
            print("✅ RAG services configuration found")
            services = config.rag_services.get('services', [])
            for service in services:
                if service.get('name') == 'ragflow':
                    print(f"✅ RagFlow service configured:")
                    print(f"   - Base URL: {service.get('base_url', 'Not set')}")
                    print(f"   - Dataset ID: {service.get('dataset_id', 'Not set')}")
                    print(f"   - API Key: {'Set' if service.get('api_key') else 'Not set'}")
                    break
            else:
                print("⚠️  RagFlow service not found in configuration")
        else:
            print("⚠️  No RAG services configuration found")
        
        return True
        
    except Exception as e:
        print(f"❌ Configuration error: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run all RAG API tests"""
    print("🚀 Starting RAG API Tests\n")
    
    tests = [
        test_rag_api_imports,
        test_rag_api_models,
        test_application_setup,
        test_rag_service_config
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
        print()
    
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All RAG API tests passed! The implementation is ready to use.")
        print("\n📋 Available RAG API Endpoints:")
        print("   - POST /api/rag/files/upload - Upload documents")
        print("   - GET  /api/rag/files - List user files")
        print("   - GET  /api/rag/files/{file_id} - Get file details")
        print("   - GET  /api/rag/files/{file_id}/status - Check file status")
        print("   - POST /api/rag/files/{file_id}/parse - Trigger parsing")
        print("   - DELETE /api/rag/files - Delete files")
        print("   - POST /api/rag/retrieve - Retrieve documents")
        print("   - POST /api/rag/generate - Generate RAG responses")
        print("   - POST /api/rag/search - Search documents")
    else:
        print("❌ Some tests failed. Please check the errors above.")
        return 1
    
    return 0

if __name__ == "__main__":
    exit(main())
